import { useEffect, useState } from 'react'
import { useRouterState } from '@tanstack/react-router'

// Page title mapping based on routes
const pageTitles: Record<string, string> = {
    '/': 'Рабочий стол',
    '/budget': 'Бюджет',
    '/gantt': 'Диаграмма Ганта',
    '/team': 'Команда',
    '/network': 'Сетевой график',
    '/reports': 'Отчёты',
    '/calendar': 'Календарь',
}

function Clock() {
    const [time, setTime] = useState(new Date())

    useEffect(() => {
        const timer = setInterval(() => {
            setTime(new Date())
        }, 1000)

        return () => clearInterval(timer)
    }, [])

    const formatTime = (date: Date) => {
        return date.toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
        })
    }

    return (
        <div className="text-sm font-mono text-muted-foreground">
            {formatTime(time)}
        </div>
    )
}

export function AppHeader() {
    const router = useRouterState()
    const currentPath = router.location.pathname
    const pageTitle = pageTitles[currentPath] || 'Страница'

    return (
        <header className="flex h-16 items-center justify-between border-b bg-background px-6">
            <div className="flex items-center">
                <h1 className="text-xl font-semibold text-foreground">
                    {pageTitle}
                </h1>
            </div>
            <div className="flex items-center">
                <Clock />
            </div>
        </header>
    )
}
