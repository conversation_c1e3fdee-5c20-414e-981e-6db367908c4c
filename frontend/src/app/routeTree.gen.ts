/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TeamRouteImport } from './routes/team'
import { Route as GanttRouteImport } from './routes/gantt'
import { Route as BudgetRouteImport } from './routes/budget'
import { Route as IndexRouteImport } from './routes/index'

const TeamRoute = TeamRouteImport.update({
  id: '/team',
  path: '/team',
  getParentRoute: () => rootRouteImport,
} as any)
const GanttRoute = GanttRouteImport.update({
  id: '/gantt',
  path: '/gantt',
  getParentRoute: () => rootRouteImport,
} as any)
const BudgetRoute = BudgetRouteImport.update({
  id: '/budget',
  path: '/budget',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/budget': typeof BudgetRoute
  '/gantt': typeof GanttRoute
  '/team': typeof TeamRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/budget': typeof BudgetRoute
  '/gantt': typeof GanttRoute
  '/team': typeof TeamRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/budget': typeof BudgetRoute
  '/gantt': typeof GanttRoute
  '/team': typeof TeamRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/budget' | '/gantt' | '/team'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/budget' | '/gantt' | '/team'
  id: '__root__' | '/' | '/budget' | '/gantt' | '/team'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  BudgetRoute: typeof BudgetRoute
  GanttRoute: typeof GanttRoute
  TeamRoute: typeof TeamRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/team': {
      id: '/team'
      path: '/team'
      fullPath: '/team'
      preLoaderRoute: typeof TeamRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/gantt': {
      id: '/gantt'
      path: '/gantt'
      fullPath: '/gantt'
      preLoaderRoute: typeof GanttRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/budget': {
      id: '/budget'
      path: '/budget'
      fullPath: '/budget'
      preLoaderRoute: typeof BudgetRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  BudgetRoute: BudgetRoute,
  GanttRoute: GanttRoute,
  TeamRoute: TeamRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
