import { createFileRoute, Outlet } from '@tanstack/react-router'
import { AppSidebar } from '@/app/layouts/sidebar'
import { AppHeader } from '@/app/layouts/header'
import { SidebarProvider } from '@/shared/ui/kit/sidebar'

export const Route = createFileRoute('/_layout')({
    component: LayoutComponent,
})

function LayoutComponent() {
    return (
        <SidebarProvider>
            <div className="flex h-screen w-full bg-background">
                {/* Sidebar */}
                <AppSidebar />

                {/* Main Content Area */}
                <div className="flex flex-1 flex-col overflow-hidden">
                    {/* Header */}
                    <AppHeader />

                    {/* Content Area */}
                    <main className="flex-1 overflow-auto p-6">
                        <div className="h-full">
                            <Outlet />
                        </div>
                    </main>

                    {/* Tools Panel Placeholder */}
                    <div className="border-t bg-muted/30 p-4">
                        <div className="text-sm text-muted-foreground">
                            Панель инструментов (заглушка)
                        </div>
                    </div>
                </div>
            </div>
        </SidebarProvider>
    )
}
