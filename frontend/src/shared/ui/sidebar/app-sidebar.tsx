import { Link, useRouterState } from '@tanstack/react-router'
import {
    Calendar,
    ChevronLeft,
    ChevronRight,
    FileText,
    Home,
    MessageSquare,
    PieChart,
    Users,
    Wallet,
    Network,
} from 'lucide-react'
import { useSidebarStore } from '@/shared/lib/stores/sidebar-store'
import { Button } from '@/shared/ui/kit/button'
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from '@/shared/ui/kit/sidebar'
import { cn } from '@/shared/lib/css'

// Navigation items configuration
const navigationItems = [
    {
        title: 'Рабочий стол',
        url: '/',
        icon: Home,
    },
    {
        title: 'Бюджет',
        url: '/budget',
        icon: Wallet,
    },
    {
        title: 'Диаграмма Ганта',
        url: '/gantt',
        icon: PieChart,
    },
    {
        title: 'Команда',
        url: '/team',
        icon: Users,
    },
    {
        title: 'Сетевой график',
        url: '/network',
        icon: Network,
    },
    {
        title: 'Отчёты',
        url: '/reports',
        icon: FileText,
    },
    {
        title: 'Календарь',
        url: '/calendar',
        icon: Calendar,
    },
]

export function AppSidebar() {
    const { isCollapsed, toggleCollapsed } = useSidebarStore()
    const router = useRouterState()
    const currentPath = router.location.pathname

    return (
        <Sidebar
            collapsible="icon"
            className={cn(
                'border-r border-sidebar-border bg-sidebar text-sidebar-foreground',
                isCollapsed && 'w-16',
            )}
        >
            <SidebarHeader className="border-b border-sidebar-border p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-sidebar-primary text-sidebar-primary-foreground font-bold text-sm">
                            S
                        </div>
                        {!isCollapsed && (
                            <span className="font-semibold text-lg">Simbios</span>
                        )}
                    </div>
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleCollapsed}
                        className="h-6 w-6 text-sidebar-foreground hover:bg-sidebar-accent"
                    >
                        {isCollapsed ? (
                            <ChevronRight className="h-4 w-4" />
                        ) : (
                            <ChevronLeft className="h-4 w-4" />
                        )}
                    </Button>
                </div>
            </SidebarHeader>

            <SidebarContent className="p-2">
                <SidebarMenu>
                    {navigationItems.map((item) => (
                        <SidebarMenuItem key={item.url}>
                            <SidebarMenuButton
                                asChild
                                isActive={currentPath === item.url}
                                className={cn(
                                    'w-full justify-start gap-3 px-3 py-2',
                                    currentPath === item.url &&
                                        'bg-sidebar-accent text-sidebar-accent-foreground',
                                )}
                            >
                                <Link to={item.url}>
                                    <item.icon className="h-4 w-4" />
                                    {!isCollapsed && (
                                        <span className="truncate">
                                            {item.title}
                                        </span>
                                    )}
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border p-2">
                <Button
                    variant="default"
                    className={cn(
                        'w-full justify-start gap-3 bg-sidebar-primary hover:bg-sidebar-primary/90',
                        isCollapsed && 'px-2',
                    )}
                >
                    <MessageSquare className="h-4 w-4" />
                    {!isCollapsed && <span>Мессенджер</span>}
                </Button>
            </SidebarFooter>
        </Sidebar>
    )
}
